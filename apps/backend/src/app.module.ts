import { CacheModule } from '@nestjs/cache-manager';
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { APP_INTERCEPTOR } from '@nestjs/core';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { ScheduleModule } from '@nestjs/schedule';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CommandRunnerModule } from 'nest-commander';
import { KeycloakConnectModule } from 'nest-keycloak-connect';
import { keycloakConfiguration, keycloakProviders } from './app.auth';
import { appDatasource } from './app.datasource';
import { DeliveryNotificationListener } from './application/listener/delivery-notification.listener';
import { NotificationListener } from './application/listener/notification.listener';
import { TourAssignmentMapper } from './application/mappers/tour-assignment.mapper';
import { XmlTourImportService } from './application/mappers/xml-import/xml-tour-import.service';
import { ClientService } from './application/service/client.service';
import { ColorService } from './application/service/color.service';
import { DeliveryNoteFileService } from './application/service/delivery-note-file.service';
import { DeliveryNotificationService } from './application/service/delivery-notification.service';
import { EmailTemplateRendererService } from './application/service/email-template-renderer.service';
import { FileService } from './application/service/file.service';
import { HolidayService } from './application/service/holiday.service';
import { ImportService } from './application/service/import.service';
import { IncidentTypeService } from './application/service/incident-type.service';
import { LogisticsEquipmentTypeService } from './application/service/logistics-equipment-type.service';
import { NotificationService } from './application/service/notification.service';
import { SftpImportService } from './application/service/sftp-import.service';
import { StopService } from './application/service/stop.service';
import { TourAssignmentService } from './application/service/tour-assignment.service';
import { TourService } from './application/service/tour.service';
import { UserQueryService } from './application/service/user-query.service';
import { UserService } from './application/service/user.service';
import { VehicleService } from './application/service/vehicle.service';
import { CompleteStopDeliveryUseCase } from './application/use-case/complete-stop-delivery.use-case';
import { ControlTourEquipmentUseCase } from './application/use-case/control-tour-equipment.use-case';
import { ImportXmlFilesFromFtpUseCase } from './application/use-case/import-xml-files-from-ftp.use-case';
import { ImportXmlUseCase } from './application/use-case/import-xml-use-case';
import { LoadStopUseCase } from './application/use-case/load-stop.use-case';
import { PreloadStopUseCase } from './application/use-case/preload-stop.use-case';
import { HolidayEntity } from './domain/entity/holiday.entity';
import { LogisticsEquipmentTypeEntity } from './domain/entity/logistics-equipment-type.entity';
import { NotificationEntity } from './domain/entity/notification.entity';
import { TourAssignmentEntity } from './domain/entity/tour-assignment.entity';
import { VehicleEntity } from './domain/entity/vehicle.entity';
import { AssignTourCommand } from './infrastructure/command/assign-tour.command';
import { DuplicateToursCommand } from './infrastructure/command/duplicate-tours.command';
import { ImportXmlToursFtpCommand } from './infrastructure/command/import-xml-tours-ftp.command';
import { ImportXmlToursCommand } from './infrastructure/command/import-xml-tours.command';
import { SeedKeycloakRolesCommand } from './infrastructure/command/seed-keycloak-roles.command';
import { SyncHolidaysCommand } from './infrastructure/command/sync-holidays.command';
import { FileController } from './infrastructure/controller/common/file.controller';
import { HolidayController } from './infrastructure/controller/common/holiday.controller';
import { UserController } from './infrastructure/controller/common/user.controller';
import { IncidentTypeDeliverController } from './infrastructure/controller/deliver/incident-type-deliver.controller';
import { LogisticsEquipmentTypeDeliverController } from './infrastructure/controller/deliver/logistics-equipment-type-deliver.controller';
import { StopDeliverController } from './infrastructure/controller/deliver/stop-deliver.controller';
import { TourDeliverController } from './infrastructure/controller/deliver/tour-deliver.controller';
import { VehicleDeliverController } from './infrastructure/controller/deliver/vehicle-deliver.controller';
import { ClientManagerController } from './infrastructure/controller/manager/client-manager.controller';
import { ImportManagerController } from './infrastructure/controller/manager/import-manager.controller';
import { IncidentTypeManagerController } from './infrastructure/controller/manager/incident-type-manager.controller';
import { LogisticsEquipmentTypeManagerController } from './infrastructure/controller/manager/logistics-equipment-type-manager.controller';
import { NotificationManagerController } from './infrastructure/controller/manager/notification-manager.controller';
import { StopManagerController } from './infrastructure/controller/manager/stop-manager.controller';
import { TourAssignmentManagerController } from './infrastructure/controller/manager/tour-assignment-manager.controller';
import { TourManagerController } from './infrastructure/controller/manager/tour-manager.controller';
import { UserManagerController } from './infrastructure/controller/manager/user-manager.controller';
import { VehicleManagerController } from './infrastructure/controller/manager/vehicle-manager.controller';
import { TourReceptionistController } from './infrastructure/controller/receptionist/tour-receptionist.controller';
import { CurrentUserInterceptor } from './infrastructure/interceptor/current-user.interceptor';
import { EmailModule } from './infrastructure/module/email.module';
import { ServeStaticModule } from './infrastructure/module/serve-static.module';
import { ClientRepository } from './infrastructure/repository/client.repository';
import { DeliveryNoteRepository } from './infrastructure/repository/delivery-note.repository';
import { FileRepository } from './infrastructure/repository/file.repository';
import { HolidayRepository } from './infrastructure/repository/holiday.repository';
import { ImportRepository } from './infrastructure/repository/import.repository';
import { IncidentTypeRepository } from './infrastructure/repository/incident-type.repository';
import { LogisticsEquipmentDetailsRepository } from './infrastructure/repository/logistics-equipment-details.repository';
import { LogisticsEquipmentTypeRepository } from './infrastructure/repository/logistics-equipment-type.repository';
import { NotificationRepository } from './infrastructure/repository/notification.repository';
import { ShipmentLineRepository } from './infrastructure/repository/shipment-line.repository';
import { StopRepository } from './infrastructure/repository/stop.repository';
import { TourAssignmentRepository } from './infrastructure/repository/tour-assignment.repository';
import { TourRepository } from './infrastructure/repository/tour.repository';
import { UserRepository } from './infrastructure/repository/user.repository';
import { VehicleRepository } from './infrastructure/repository/vehicle.repository';
import { HtmlRenderService } from './infrastructure/service/html-render/html-render.service';
import { KeycloakAdminClient } from './infrastructure/service/keycloak-admin.client';
import { KeycloakAdminService } from './infrastructure/service/keycloak-admin.service';
import { S3Service } from './infrastructure/service/s3.service';
import { SftpService } from './infrastructure/service/sftp.service';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    ServeStaticModule,
    CacheModule.register(),
    TypeOrmModule.forRoot({
      ...appDatasource.options,
      entities: [
        ...(Array.isArray(appDatasource.options.entities) ? appDatasource.options.entities : []),
        TourAssignmentEntity,
        LogisticsEquipmentTypeEntity,
        VehicleEntity,
        HolidayEntity,
        NotificationEntity,
      ],
    }),
    KeycloakConnectModule.register(keycloakConfiguration),
    CommandRunnerModule,
    EventEmitterModule.forRoot(),
    EmailModule,
    ScheduleModule.forRoot(),
  ],
  controllers: [
    UserController,
    FileController,
    HolidayController,
    ImportManagerController,
    TourAssignmentManagerController,
    ClientManagerController,
    TourManagerController,
    StopManagerController,
    IncidentTypeManagerController,
    LogisticsEquipmentTypeManagerController,
    UserManagerController,
    VehicleManagerController,
    IncidentTypeDeliverController,
    LogisticsEquipmentTypeDeliverController,
    StopDeliverController,
    TourDeliverController,
    VehicleDeliverController,
    NotificationManagerController,
    TourReceptionistController,
  ],
  providers: [
    SeedKeycloakRolesCommand,
    ImportXmlToursCommand,
    ImportXmlToursFtpCommand,
    AssignTourCommand,
    DuplicateToursCommand,
    SyncHolidaysCommand,
    ...keycloakProviders,
    KeycloakAdminService,
    UserQueryService,
    {
      provide: APP_INTERCEPTOR,
      useClass: CurrentUserInterceptor,
    },
    UserService,
    ColorService,
    ImportService,
    ImportRepository,
    UserRepository,
    FileService,
    FileRepository,
    DeliveryNoteFileService,
    XmlTourImportService,
    ImportXmlUseCase,
    ImportXmlFilesFromFtpUseCase,
    DeliveryNoteRepository,
    ShipmentLineRepository,
    StopRepository,
    TourAssignmentRepository,
    TourRepository,
    ClientRepository,
    ClientService,
    TourAssignmentService,
    TourAssignmentMapper,
    TourService,
    StopService,
    IncidentTypeService,
    IncidentTypeRepository,
    LogisticsEquipmentTypeService,
    LogisticsEquipmentTypeRepository,
    LogisticsEquipmentDetailsRepository,
    VehicleService,
    VehicleRepository,
    HolidayService,
    HolidayRepository,
    CompleteStopDeliveryUseCase,
    LoadStopUseCase,
    PreloadStopUseCase,
    ControlTourEquipmentUseCase,
    SftpImportService,
    S3Service,
    SftpService,
    DeliveryNotificationService,
    EmailTemplateRendererService,
    DeliveryNotificationListener,
    NotificationListener,
    NotificationService,
    NotificationRepository,
    TourAssignmentRepository,
    HtmlRenderService,
    {
      provide: KeycloakAdminClient,
      useFactory: async (): Promise<KeycloakAdminClient> => {
        const keycloakAdminClient = new KeycloakAdminClient();
        await keycloakAdminClient.initializeClient();

        return keycloakAdminClient;
      },
    },
  ],
})
export class AppModule {}
