import { Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { StopCompletedEvent } from '../../domain/event/stop-completed.event';
import { StopEntity } from '../../domain/entity/stop.entity';
import { DeliveryNotificationService } from '../service/delivery-notification.service';

@Injectable()
export class DeliveryNotificationListener {
  constructor(private readonly deliveryNotificationService: DeliveryNotificationService) {}

  @OnEvent('stop.completed', { async: true })
  async handleStopCompleted(event: StopCompletedEvent): Promise<void> {
    const stop = event.stop;

    await this.deliveryNotificationService.sendDeliveryNotification({
      stopId: stop.id,
      tourNumber: stop.tour?.tourIdentifier?.originalNumber || 'Unknown',
      deliveryDate:
        typeof stop.tour?.deliveryDate === 'string'
          ? new Date(stop.tour.deliveryDate)
          : stop.tour?.deliveryDate || new Date(),
      deliveryAddress: this.getStopAddress(stop),
      completedAt: stop.completion.completedAt,
      deliveryStatus: stop.completion.deliveryStatus,
      hasSignature: !!stop.completion.signatureFile,
      hasPhoto: !!stop.completion.proofPhotoFile,
      operatorInputEmail: event.operatorInputEmail,
      clientEntity: stop.client
        ? {
            id: stop.client.id,
            name: stop.client.name || 'Unknown Client',
            email: stop.client.email,
          }
        : null,
      originalClientInfo: stop.originalClientInfo
        ? {
            name: stop.originalClientInfo.name,
            email: stop.originalClientInfo.email,
          }
        : null,
    });
  }

  private getStopAddress(stop: StopEntity): string {
    const address = stop.originalClientInfo?.address;

    if (!address) {
      return 'Unknown address';
    }

    const parts = [address.line1, address.line2, address.line3, address.line4].filter(Boolean);

    return parts.length > 0 ? parts.join(', ') : 'Unknown address';
  }
}
