import { BadRequestException, Logger, NotFoundException } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Test, TestingModule } from '@nestjs/testing';
import { ClientEntity } from '../../domain/entity/client.entity';
import { DeliveryStatus } from '../../domain/entity/stop-completion.entity';
import { StopEntity } from '../../domain/entity/stop.entity';
import { ClientRepository } from '../../infrastructure/repository/client.repository';
import { FileRepository } from '../../infrastructure/repository/file.repository';
import { IncidentTypeRepository } from '../../infrastructure/repository/incident-type.repository';
import { LogisticsEquipmentDetailsRepository } from '../../infrastructure/repository/logistics-equipment-details.repository';
import { LogisticsEquipmentTypeRepository } from '../../infrastructure/repository/logistics-equipment-type.repository';
import { StopRepository } from '../../infrastructure/repository/stop.repository';
import { S3Service } from '../../infrastructure/service/s3.service';
import { CompleteStopDeliveryDto } from '../dto/stop/complete-stop-delivery.dto';
import { FileService } from '../service/file.service';
import { CompleteStopDeliveryUseCase } from './complete-stop-delivery.use-case';

describe('CompleteStopDeliveryUseCase', () => {
  let useCase: CompleteStopDeliveryUseCase;
  let stopRepository: jest.Mocked<StopRepository>;
  let _incidentTypeRepository: jest.Mocked<IncidentTypeRepository>;
  let _fileRepository: jest.Mocked<FileRepository>;
  let _s3Service: jest.Mocked<S3Service>;
  let _stopLogisticsEquipmentRepository: jest.Mocked<LogisticsEquipmentDetailsRepository>;
  let _logisticsEquipmentTypeRepository: jest.Mocked<LogisticsEquipmentTypeRepository>;
  let clientRepository: jest.Mocked<ClientRepository>;
  let logger: jest.SpyInstance;

  const mockStopRepository = {
    findOne: jest.fn(),
    save: jest.fn(),
  };

  const mockIncidentTypeRepository = {
    findOne: jest.fn(),
  };

  const mockFileRepository = {
    create: jest.fn(),
    save: jest.fn(),
  };

  const mockS3Service = {
    upload: jest.fn(),
  };

  const mockLogisticsEquipmentDetailsRepository = {
    create: jest.fn(),
    save: jest.fn(),
    delete: jest.fn(),
    find: jest.fn(),
  };

  const mockLogisticsEquipmentTypeRepository = {
    find: jest.fn(),
  };

  const mockClientRepository = {
    save: jest.fn(),
  };

  const mockFileService = {
    createFile: jest.fn(),
  };

  const mockEventEmitter = {
    emit: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CompleteStopDeliveryUseCase,
        {
          provide: StopRepository,
          useValue: mockStopRepository,
        },
        {
          provide: IncidentTypeRepository,
          useValue: mockIncidentTypeRepository,
        },
        {
          provide: FileRepository,
          useValue: mockFileRepository,
        },
        {
          provide: S3Service,
          useValue: mockS3Service,
        },
        {
          provide: LogisticsEquipmentDetailsRepository,
          useValue: mockLogisticsEquipmentDetailsRepository,
        },
        {
          provide: LogisticsEquipmentTypeRepository,
          useValue: mockLogisticsEquipmentTypeRepository,
        },
        {
          provide: ClientRepository,
          useValue: mockClientRepository,
        },
        {
          provide: FileService,
          useValue: mockFileService,
        },
        {
          provide: EventEmitter2,
          useValue: mockEventEmitter,
        },
      ],
    }).compile();

    useCase = module.get<CompleteStopDeliveryUseCase>(CompleteStopDeliveryUseCase);
    stopRepository = module.get(StopRepository);
    _incidentTypeRepository = module.get(IncidentTypeRepository);
    _fileRepository = module.get(FileRepository);
    _s3Service = module.get(S3Service);
    _stopLogisticsEquipmentRepository = module.get(LogisticsEquipmentDetailsRepository);
    _logisticsEquipmentTypeRepository = module.get(LogisticsEquipmentTypeRepository);
    clientRepository = module.get(ClientRepository);

    // Mock logger
    logger = jest.spyOn(Logger.prototype, 'log').mockImplementation();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Client email update', () => {
    const stopId = '123e4567-e89b-12d3-a456-426614174000';
    const clientId = '456e7890-e89b-12d3-a456-426614174000';

    const mockClient: Partial<ClientEntity> = {
      id: clientId,
      code: 'CLIENT001',
      name: 'Test Client',
      email: '<EMAIL>',
    };

    const mockStop: Partial<StopEntity> = {
      id: stopId,
      client: mockClient as ClientEntity,
      completion: {
        deliveryStatus: DeliveryStatus.PENDING,
      } as any,
    };

    beforeEach(() => {
      // Reset mocks and setup fresh data for each test
      jest.clearAllMocks();

      const freshMockStop: Partial<StopEntity> = {
        id: stopId,
        client: { ...mockClient } as ClientEntity,
        completion: {
          deliveryStatus: DeliveryStatus.PENDING,
        } as any,
      };

      stopRepository.findOne.mockResolvedValue(freshMockStop as StopEntity);
      stopRepository.save.mockImplementation((stop) => Promise.resolve(stop as StopEntity));
      clientRepository.save.mockImplementation((client) => Promise.resolve(client as ClientEntity));
    });

    it('should update client email when a new email is provided and client exists', async () => {
      // Arrange
      const dto: CompleteStopDeliveryDto = {
        clientEmail: '<EMAIL>',
        signatureFile: {
          base64:
            'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
          filename: 'signature.png',
          mimeType: 'image/png',
        },
      };

      // Mock file upload
      mockFileService.createFile.mockResolvedValue({ id: 'file-id' } as any);

      // Act
      await useCase.execute(stopId, dto);

      // Assert
      expect(clientRepository.save).toHaveBeenCalledWith(
        expect.objectContaining({
          id: clientId,
          email: '<EMAIL>',
        }),
      );
      expect(logger).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Client email updated during stop completion',
          clientId,
          clientCode: 'CLIENT001',
          stopId,
          previousEmail: '<EMAIL>',
          newEmail: '<EMAIL>',
        }),
      );
    });

    it('should normalize email before saving (trim and lowercase)', async () => {
      // Arrange
      const dto: CompleteStopDeliveryDto = {
        clientEmail: '  <EMAIL>  ',
        signatureFile: {
          base64:
            'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
          filename: 'signature.png',
          mimeType: 'image/png',
        },
      };

      // Mock file upload
      mockFileService.createFile.mockResolvedValue({ id: 'file-id' } as any);

      // Act
      await useCase.execute(stopId, dto);

      // Assert
      expect(clientRepository.save).toHaveBeenCalledWith(
        expect.objectContaining({
          email: '<EMAIL>',
        }),
      );
    });

    it('should not update client email when the email is the same', async () => {
      // Arrange
      const dto: CompleteStopDeliveryDto = {
        clientEmail: '<EMAIL>',
        signatureFile: {
          base64:
            'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
          filename: 'signature.png',
          mimeType: 'image/png',
        },
      };

      // Mock file upload
      mockFileService.createFile.mockResolvedValue({ id: 'file-id' } as any);

      // Act
      await useCase.execute(stopId, dto);

      // Assert
      expect(clientRepository.save).not.toHaveBeenCalled();
      expect(logger).not.toHaveBeenCalled();
    });

    it('should not update client email when the email is the same after normalization', async () => {
      // Arrange
      const dto: CompleteStopDeliveryDto = {
        clientEmail: '  <EMAIL>  ',
        signatureFile: {
          base64:
            'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
          filename: 'signature.png',
          mimeType: 'image/png',
        },
      };

      // Mock file upload
      mockFileService.createFile.mockResolvedValue({ id: 'file-id' } as any);

      // Act
      await useCase.execute(stopId, dto);

      // Assert
      expect(clientRepository.save).not.toHaveBeenCalled();
      expect(logger).not.toHaveBeenCalled();
    });

    it('should not update email when no client entity is linked to the stop', async () => {
      // Arrange
      const stopWithoutClient: Partial<StopEntity> = {
        ...mockStop,
        client: null,
      };
      stopRepository.findOne.mockResolvedValue(stopWithoutClient as StopEntity);

      const dto: CompleteStopDeliveryDto = {
        clientEmail: '<EMAIL>',
        signatureFile: {
          base64:
            'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
          filename: 'signature.png',
          mimeType: 'image/png',
        },
      };

      // Mock file upload
      mockFileService.createFile.mockResolvedValue({ id: 'file-id' } as any);

      // Act
      await useCase.execute(stopId, dto);

      // Assert
      expect(clientRepository.save).not.toHaveBeenCalled();
      expect(logger).not.toHaveBeenCalled();
    });

    it('should not update email when no email is provided in the DTO', async () => {
      // Arrange
      const dto: CompleteStopDeliveryDto = {
        signatureFile: {
          base64:
            'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
          filename: 'signature.png',
          mimeType: 'image/png',
        },
      };

      // Mock file upload
      mockFileService.createFile.mockResolvedValue({ id: 'file-id' } as any);

      // Act
      await useCase.execute(stopId, dto);

      // Assert
      expect(clientRepository.save).not.toHaveBeenCalled();
      expect(logger).not.toHaveBeenCalled();
    });

    it('should handle client without existing email', async () => {
      // Arrange
      const clientWithoutEmail: Partial<ClientEntity> = {
        ...mockClient,
        email: undefined,
      };
      const stopWithClientWithoutEmail: Partial<StopEntity> = {
        id: stopId,
        client: clientWithoutEmail as ClientEntity,
        completion: {
          deliveryStatus: DeliveryStatus.PENDING,
        } as any,
      };
      stopRepository.findOne.mockResolvedValue(stopWithClientWithoutEmail as StopEntity);

      const dto: CompleteStopDeliveryDto = {
        clientEmail: '<EMAIL>',
        signatureFile: {
          base64:
            'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
          filename: 'signature.png',
          mimeType: 'image/png',
        },
      };

      // Mock file upload
      mockFileService.createFile.mockResolvedValue({ id: 'file-id' } as any);

      // Act
      await useCase.execute(stopId, dto);

      // Assert
      expect(clientRepository.save).toHaveBeenCalledWith(
        expect.objectContaining({
          email: '<EMAIL>',
        }),
      );
      expect(logger).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Client email updated during stop completion',
          previousEmail: undefined,
          newEmail: '<EMAIL>',
        }),
      );
    });
  });

  describe('execute - basic flow', () => {
    it('should throw NotFoundException when stop does not exist', async () => {
      // Arrange
      stopRepository.findOne.mockResolvedValue(null);
      const dto: CompleteStopDeliveryDto = {};

      // Act & Assert
      await expect(useCase.execute('non-existent-id', dto)).rejects.toThrow(NotFoundException);
    });

    it('should throw BadRequestException when stop is already completed', async () => {
      // Arrange
      const completedStop: Partial<StopEntity> = {
        id: '123',
        completion: {
          deliveryStatus: DeliveryStatus.COMPLETED,
        } as any,
      };
      stopRepository.findOne.mockResolvedValue(completedStop as StopEntity);
      const dto: CompleteStopDeliveryDto = {};

      // Act & Assert
      await expect(useCase.execute('123', dto)).rejects.toThrow(BadRequestException);
    });
  });
});
