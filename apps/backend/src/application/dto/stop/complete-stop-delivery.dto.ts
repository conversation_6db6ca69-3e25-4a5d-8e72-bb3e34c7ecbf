import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsEmail,
  IsEnum,
  IsInt,
  IsOptional,
  IsString,
  IsUUID,
  Min,
  ValidateIf,
  ValidateNested,
} from 'class-validator';
import { DeliveryCompletionType } from '../../../domain/entity/stop-completion.entity';
import { FileUploadDto } from '../file/file-upload.dto';
import { LogisticsEquipmentDetailDto } from './logistics-equipment-detail.dto';

export class EquipmentCountDto {
  @IsOptional()
  @IsInt()
  @Min(0)
  palletCount?: number;

  @IsOptional()
  @IsInt()
  @Min(0)
  rollCount?: number;

  @IsOptional()
  @IsInt()
  @Min(0)
  packageCount?: number;
}

export class CompleteStopDeliveryDto {
  @IsOptional()
  @ValidateNested()
  @Type(() => FileUploadDto)
  signatureFile?: FileUploadDto;

  @IsOptional()
  @ValidateNested()
  @Type(() => FileUploadDto)
  photoFile?: FileUploadDto;

  @IsOptional()
  @ValidateNested()
  @Type(() => EquipmentCountDto)
  returnedEquipment?: EquipmentCountDto;

  @IsOptional()
  @ValidateNested()
  @Type(() => EquipmentCountDto)
  unloadedEquipment?: EquipmentCountDto;

  @ApiProperty({
    description: 'Detailed returned equipment by specific type',
    type: [LogisticsEquipmentDetailDto],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => LogisticsEquipmentDetailDto)
  returnedEquipmentDetails?: LogisticsEquipmentDetailDto[];

  @ApiProperty({
    description: 'Detailed unloaded equipment by specific type',
    type: [LogisticsEquipmentDetailDto],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => LogisticsEquipmentDetailDto)
  unloadedEquipmentDetails?: LogisticsEquipmentDetailDto[];

  // Incident
  @IsOptional()
  @IsUUID()
  incidentTypeId?: string;

  @IsOptional()
  @IsEnum(DeliveryCompletionType)
  @ValidateIf((o) => o.incidentTypeId !== undefined)
  deliveryCompletionType?: DeliveryCompletionType;

  @IsOptional()
  @IsString()
  comments?: string;

  @IsOptional()
  @IsEmail()
  @ApiProperty({
    description: 'Email du client saisi/corrigé par le livreur',
    required: false,
  })
  clientEmail?: string;
}
