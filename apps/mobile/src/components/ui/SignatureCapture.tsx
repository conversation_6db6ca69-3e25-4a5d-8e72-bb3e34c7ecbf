import { IonText } from '@ionic/react';
import { useRef, useState, useEffect, useCallback } from 'react';
import { trash, close, checkmark } from 'ionicons/icons';
import InlineButtons from './stylized/InlineButtons';

interface SignatureCaptureProps {
  onSignatureCapture: (signatureBase64: string) => void;
  onCancel: () => void;
}

const SignatureCapture: React.FC<SignatureCaptureProps> = ({ onSignatureCapture, onCancel }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [isDrawing, setIsDrawing] = useState(false);
  const [hasSignature, setHasSignature] = useState(false);
  const [canvasReady, setCanvasReady] = useState(false);

  // Fonction pour initialiser le canvas
  const setupCanvas = useCallback(() => {
    const canvas = canvasRef.current;

    if (!canvas) {
      console.log('Canvas ref not available');

      return false;
    }

    const ctx = canvas.getContext('2d');

    if (!ctx) {
      console.log('Canvas context not available');

      return false;
    }

    // Attendre que le canvas soit visible dans le DOM
    const rect = canvas.getBoundingClientRect();

    if (rect.width === 0 || rect.height === 0) {
      console.log('Canvas not yet visible, dimensions:', rect);

      return false;
    }

    console.log('Setting up canvas with dimensions:', rect);

    // Définir une taille minimum pour éviter les canvas trop petits
    const minWidth = 300;
    const minHeight = 200;
    const actualWidth = Math.max(rect.width, minWidth);
    const actualHeight = Math.max(rect.height, minHeight);

    // Configuration avec device pixel ratio pour des lignes nettes
    const ratio = window.devicePixelRatio || 1;

    // Définir les dimensions du canvas en pixels
    canvas.width = actualWidth * ratio;
    canvas.height = actualHeight * ratio;

    // Définir la taille CSS d'affichage
    canvas.style.width = `${actualWidth}px`;
    canvas.style.height = `${actualHeight}px`;

    // Mettre à l'échelle le contexte pour dessiner correctement
    ctx.scale(ratio, ratio);

    // Configuration du style de dessin
    ctx.strokeStyle = '#000000';
    ctx.lineWidth = 3;
    ctx.lineCap = 'round';
    ctx.lineJoin = 'round';

    // Fond blanc
    ctx.fillStyle = '#ffffff';
    ctx.fillRect(0, 0, actualWidth, actualHeight);

    console.log('Canvas setup complete:', {
      canvasWidth: canvas.width,
      canvasHeight: canvas.height,
      styleWidth: canvas.style.width,
      styleHeight: canvas.style.height,
      ratio,
    });

    return true;
  }, []);

  // Effet pour configurer le canvas avec retry
  useEffect(() => {
    let retryCount = 0;
    const maxRetries = 5;

    const initCanvas = () => {
      if (setupCanvas()) {
        setCanvasReady(true);

        return;
      }

      retryCount++;

      if (retryCount < maxRetries) {
        console.log(`Retrying canvas setup (${retryCount}/${maxRetries})`);
        setTimeout(initCanvas, 100 * retryCount); // Délai progressif
      } else {
        console.error('Failed to setup canvas after maximum retries');
      }
    };

    // Attendre que le composant soit monté
    setTimeout(initCanvas, 50);

    // Observer pour les changements de taille
    let resizeObserver: ResizeObserver | null = null;

    if (canvasRef.current && 'ResizeObserver' in window) {
      resizeObserver = new ResizeObserver(() => {
        console.log('Canvas resize detected');
        setTimeout(() => {
          if (setupCanvas()) {
            setCanvasReady(true);
          }
        }, 100);
      });

      resizeObserver.observe(canvasRef.current);
    }

    return () => {
      if (resizeObserver) {
        resizeObserver.disconnect();
      }
    };
  }, [setupCanvas]);

  const getCoordinates = (event: React.TouchEvent | React.MouseEvent) => {
    const canvas = canvasRef.current;

    if (!canvas) {
      return { x: 0, y: 0 };
    }

    const rect = canvas.getBoundingClientRect();

    if ('touches' in event) {
      const touch = event.touches[0];

      return {
        x: touch.clientX - rect.left,
        y: touch.clientY - rect.top,
      };
    } else {
      return {
        x: event.clientX - rect.left,
        y: event.clientY - rect.top,
      };
    }
  };

  const startDrawing = (event: React.TouchEvent | React.MouseEvent) => {
    if (!canvasReady) {
      console.log('Canvas not ready, skipping draw');

      return;
    }

    // Prévenir le comportement par défaut uniquement pour la souris
    if ('touches' in event) {
      // Événement tactile - ne pas appeler preventDefault()
    } else {
      event.preventDefault();
    }

    setIsDrawing(true);
    setHasSignature(true);

    const canvas = canvasRef.current;

    if (!canvas) {
      return;
    }

    const ctx = canvas.getContext('2d');

    if (!ctx) {
      return;
    }

    const { x, y } = getCoordinates(event);
    ctx.beginPath();
    ctx.moveTo(x, y);
  };

  const draw = (event: React.TouchEvent | React.MouseEvent) => {
    if (!isDrawing || !canvasReady) {
      return;
    }

    // Prévenir le comportement par défaut uniquement pour la souris
    if ('touches' in event) {
      // Événement tactile - ne pas appeler preventDefault()
    } else {
      event.preventDefault();
    }

    const canvas = canvasRef.current;

    if (!canvas) {
      return;
    }

    const ctx = canvas.getContext('2d');

    if (!ctx) {
      return;
    }

    const { x, y } = getCoordinates(event);
    ctx.lineTo(x, y);
    ctx.stroke();
  };

  const stopDrawing = () => {
    setIsDrawing(false);
  };

  const clearCanvas = () => {
    if (!canvasReady) {
      return;
    }

    const canvas = canvasRef.current;

    if (!canvas) {
      return;
    }

    const ctx = canvas.getContext('2d');

    if (!ctx) {
      return;
    }

    // Récupérer les dimensions actuelles
    const rect = canvas.getBoundingClientRect();
    const actualWidth = Math.max(rect.width, 300);
    const actualHeight = Math.max(rect.height, 200);

    // Effacer tout le canvas
    ctx.clearRect(0, 0, actualWidth, actualHeight);

    // Remettre le fond blanc
    ctx.fillStyle = '#ffffff';
    ctx.fillRect(0, 0, actualWidth, actualHeight);

    setHasSignature(false);
  };

  const saveSignature = () => {
    const canvas = canvasRef.current;

    if (!canvas || !hasSignature || !canvasReady) {
      return;
    }

    try {
      // Convertir en base64
      const dataURL = canvas.toDataURL('image/png');
      console.log('Signature saved, data URL length:', dataURL.length);
      onSignatureCapture(dataURL);
    } catch (error) {
      console.error('Error saving signature:', error);
    }
  };

  return (
    <div className="pt-6 pb-6">
      {/* Titre et description */}
      <div className="text-center mb-8">
        <IonText className="text-xl sm:text-2xl md:text-3xl font-bold text-black mb-4 block">
          Signature du destinataire
        </IonText>
        <IonText className="text-sm sm:text-base text-gray-600">
          Signez dans la zone ci-dessous avec votre doigt ou votre stylet
        </IonText>
      </div>

      {/* Zone de signature */}
      <div className="mb-8">
        <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 bg-gray-50">
          <canvas
            ref={canvasRef}
            className="w-full bg-white rounded-lg shadow-sm cursor-crosshair touch-none border border-gray-200"
            style={{
              touchAction: 'none',
              height: '200px', // Hauteur fixe minimum
              minHeight: '200px',
              maxHeight: '300px',
            }}
            onMouseDown={startDrawing}
            onMouseMove={draw}
            onMouseUp={stopDrawing}
            onMouseLeave={stopDrawing}
            onTouchStart={startDrawing}
            onTouchMove={draw}
            onTouchEnd={stopDrawing}
          />

          {/* Indicateur de chargement */}
          {!canvasReady && (
            <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-75 rounded-lg">
              <IonText className="text-sm text-gray-500">Initialisation du canvas...</IonText>
            </div>
          )}
        </div>

        {/* Indication visuelle */}
        <div className="text-center mt-4">
          <IonText className="text-xs sm:text-sm text-gray-500">
            {!canvasReady
              ? 'Préparation de la zone de signature...'
              : hasSignature
                ? 'Signature capturée'
                : 'Dessinez votre signature ci-dessus'}
          </IonText>
        </div>
      </div>

      {/* Boutons d'action avec InlineButtons */}
      <div className="space-y-4 mx-4">
        <InlineButtons
          buttons={[
            {
              label: 'Effacer',
              icon: trash,
              onClick: canvasReady ? clearCanvas : () => {},
              classNames: {
                button: `${canvasReady ? 'secondary-button-outline' : 'bg-gray-300 text-gray-500 cursor-not-allowed'} px-6 py-3 text-sm font-medium`,
              },
            },
          ]}
        />

        <InlineButtons
          buttons={[
            {
              label: 'Annuler',
              icon: close,
              onClick: onCancel,
              classNames: {
                button: 'secondary-button-outline px-8 py-3 font-medium',
              },
            },
            {
              label: 'Valider',
              icon: checkmark,
              onClick: hasSignature && canvasReady ? saveSignature : () => {},
              classNames: {
                button: `px-8 py-3 font-medium ${
                  hasSignature && canvasReady
                    ? 'primary-button'
                    : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                }`,
              },
            },
          ]}
        />
      </div>
    </div>
  );
};

export default SignatureCapture;
