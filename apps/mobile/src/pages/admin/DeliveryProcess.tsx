import { IonCheckbox, IonContent, IonIcon, IonPage, IonText } from '@ionic/react';
import { warning } from 'ionicons/icons';
import { useEffect, useRef, useState } from 'react';
import { useHistory, useParams } from 'react-router-dom';

import { useTourControlledGuard } from '../../hooks/useTourControlledGuard';
import { IStopEntity } from '../../interfaces/entity/i-stop-entity';
import { formatDate } from '../../utils/dateUtils';

import { ChevronLeft } from 'lucide-react';
import {
  IncidentData,
  SignatureData,
  StepClientComment,
  StepDelivery,
  StepEquipmentDeposit,
  StepEquipmentPickup,
  StepIncident,
  StepPayment,
  StepSignature,
  StepSuccess,
} from '../../components/services/deliverer/delivery-process';
import { StepClientCommentRef } from '../../components/services/deliverer/delivery-process/steps/StepClientComment';
import { StepEquipmentDepositRef } from '../../components/services/deliverer/delivery-process/steps/StepEquipmentDeposit';
import { StepEquipmentPickupRef } from '../../components/services/deliverer/delivery-process/steps/StepEquipmentPickup';
import { StepPaymentRef } from '../../components/services/deliverer/delivery-process/steps/StepPayment';
import { StepSignatureRef } from '../../components/services/deliverer/delivery-process/steps/StepSignature';
import {
  PaymentData,
  ReturnedEquipmentData,
  UnloadedEquipmentData,
} from '../../components/services/deliverer/delivery-process/types';
import FormRecoveryModal from '../../components/ui/FormRecoveryModal';
import InlineButtons from '../../components/ui/stylized/InlineButtons';
import { deliveryFormPersistenceService } from '../../services/DeliveryFormPersistenceService';
import { makeGetStopByIdSelector } from '../../stores/tourSlice';
import { useAppSelector } from '../../utils/redux';

interface RouteParams {
  stopId: string;
}

export enum DeliveryStep {
  DELIVERY = 'delivery',
  PAYMENT = 'payment',
  EQUIPMENT_DEPOSIT = 'equipment_deposit',
  EQUIPMENT_PICKUP = 'equipment_pickup',
  DOCUMENTATION = 'documentation',
  SIGNATURE = 'signature',
  SUCCESS = 'success',
}

interface DeliveryFormData {
  stop: IStopEntity;
  paymentData?: PaymentData;
  equipmentDepositData?: UnloadedEquipmentData;
  equipmentPickupData?: ReturnedEquipmentData;
  documentationData?: {
    photos: string[];
    comment: string;
  };
  signatureData?: SignatureData;
  incidentData?: IncidentData;
  gpsData?: {
    latitude: number;
    longitude: number;
    timestamp: number;
    accuracy?: number;
  };
}

const DeliveryProcess: React.FC = () => {
  const { stopId } = useParams<RouteParams>();
  const history = useHistory();
  const loading = useAppSelector((state) => !state.tour.isDataResolved);
  const stopSelectorFactory = makeGetStopByIdSelector();
  const stop = useAppSelector((state) => stopSelectorFactory(state, stopId));

  const [currentStep, setCurrentStep] = useState<DeliveryStep>(DeliveryStep.DELIVERY);
  const [formData, setFormData] = useState<DeliveryFormData | null>(null);
  const [isIncidentMode, setIsIncidentMode] = useState(false);
  const [isClientAbsent, setIsClientAbsent] = useState(false);

  const [showRecoveryModal, setShowRecoveryModal] = useState(false);
  const [formSummary, setFormSummary] = useState<any>(null);
  const [isLoadingPersistence, setIsLoadingPersistence] = useState(true);
  const [, forceUpdate] = useState({});

  // Refs for step components
  const stepPaymentRef = useRef<StepPaymentRef>(null);
  const stepEquipmentDepositRef = useRef<StepEquipmentDepositRef>(null);
  const stepEquipmentPickupRef = useRef<StepEquipmentPickupRef>(null);
  const stepClientCommentRef = useRef<StepClientCommentRef>(null);
  const stepSignatureRef = useRef<StepSignatureRef>(null);

  useTourControlledGuard();

  // Force le re-render pour mettre à jour l'état du bouton
  useEffect(() => {
    const interval = setInterval(() => {
      forceUpdate({});
    }, 500); // Met à jour toutes les 500ms

    return () => clearInterval(interval);
  }, [currentStep]);

  // Charger les données persistées au démarrage
  useEffect(() => {
    const loadPersistedData = async () => {
      if (!stopId) {
        setIsLoadingPersistence(false);

        return;
      }

      try {
        const isPartiallyFilled =
          await deliveryFormPersistenceService.isFormPartiallyFilled(stopId);

        if (isPartiallyFilled) {
          const summary = await deliveryFormPersistenceService.getFormSummary(stopId);

          if (summary) {
            setFormSummary(summary);
            setShowRecoveryModal(true);
          } else {
            setIsLoadingPersistence(false);
          }
        } else {
          setIsLoadingPersistence(false);
        }
      } catch (error) {
        console.error('Erreur lors du chargement des données persistées:', error);
        setIsLoadingPersistence(false);
      }
    };

    loadPersistedData();
  }, [stopId]);

  // Timeout de sécurité pour débloquer le chargement
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (isLoadingPersistence) {
        console.warn('Timeout de chargement de persistance, déblocage forcé');
        setIsLoadingPersistence(false);
      }
    }, 10000);

    return () => clearTimeout(timeoutId);
  }, [isLoadingPersistence]);

  useEffect(() => {
    if (formData || !stop || isLoadingPersistence) {
      return;
    }

    setFormData({ stop });
  }, [stop, formData, isLoadingPersistence]);

  // Fonction utilitaire pour sauvegarder et naviguer
  const saveAndNavigate = async (partialData: Partial<any>, nextStep?: DeliveryStep) => {
    try {
      await deliveryFormPersistenceService.saveFormData(stopId, {
        currentStep,
        ...partialData,
      });

      if (nextStep) {
        setCurrentStep(nextStep);
      }
    } catch (error) {
      console.error('Erreur lors de la sauvegarde:', error);
    }
  };

  // Gestionnaires pour la modal de récupération
  const handleContinueForm = async () => {
    try {
      const persistedData = await deliveryFormPersistenceService.getFormData(stopId);

      if (persistedData && stop) {
        setFormData({
          stop,
          paymentData: persistedData.paymentData,
          equipmentDepositData: persistedData.equipmentDepositData,
          equipmentPickupData: persistedData.equipmentPickupData,
          documentationData: persistedData.documentationData,
          signatureData: persistedData.signatureData,
          incidentData: persistedData.incidentData,
          gpsData: persistedData.gpsData,
        });

        setCurrentStep(persistedData.currentStep || DeliveryStep.DELIVERY);
      }

      setShowRecoveryModal(false);
      setIsLoadingPersistence(false);
    } catch (error) {
      console.error('Erreur lors de la restauration:', error);
      setShowRecoveryModal(false);
      setIsLoadingPersistence(false);
    }
  };

  const handleRestartForm = async () => {
    try {
      await deliveryFormPersistenceService.clearFormData(stopId);
      setShowRecoveryModal(false);
      setIsLoadingPersistence(false);
    } catch (error) {
      console.error('Erreur lors de la réinitialisation:', error);
      setShowRecoveryModal(false);
      setIsLoadingPersistence(false);
    }
  };

  const handleCancelRecovery = () => {
    setShowRecoveryModal(false);
    setIsLoadingPersistence(false);
    setFormSummary(null);
  };

  // Navigation globale
  const handleGlobalBack = () => {
    if (isIncidentMode) {
      setIsIncidentMode(false);

      return;
    }

    switch (currentStep) {
      case DeliveryStep.PAYMENT:
        setCurrentStep(DeliveryStep.DELIVERY);
        break;
      case DeliveryStep.EQUIPMENT_DEPOSIT:
        if (isClientAbsent) {
          setIsClientAbsent(false);
          setCurrentStep(DeliveryStep.DELIVERY);
        } else {
          setCurrentStep(DeliveryStep.PAYMENT);
        }
        break;
      case DeliveryStep.EQUIPMENT_PICKUP:
        setCurrentStep(DeliveryStep.EQUIPMENT_DEPOSIT);
        break;
      case DeliveryStep.DOCUMENTATION:
        setCurrentStep(DeliveryStep.EQUIPMENT_PICKUP);
        break;
      case DeliveryStep.SIGNATURE:
        // Si client absent, on ne devrait pas arriver ici, mais au cas où
        if (isClientAbsent) {
          setCurrentStep(DeliveryStep.EQUIPMENT_PICKUP);
        } else {
          setCurrentStep(DeliveryStep.DOCUMENTATION);
        }
        break;
      case DeliveryStep.SUCCESS:
        history.goBack();
        break;
      case DeliveryStep.DELIVERY:
      default:
        history.goBack();
        break;
    }
  };

  const canGoBack = () => {
    return (
      (currentStep !== DeliveryStep.DELIVERY && currentStep !== DeliveryStep.SUCCESS) ||
      isIncidentMode
    );
  };

  const getBackButtonText = () => {
    if (isIncidentMode) {
      return 'Fermer incident';
    }

    const backButtonTexts = {
      [DeliveryStep.DELIVERY]: 'Retour',
      [DeliveryStep.PAYMENT]: 'Livraison',
      [DeliveryStep.EQUIPMENT_DEPOSIT]: isClientAbsent ? 'Livraison' : 'Paiement',
      [DeliveryStep.EQUIPMENT_PICKUP]: 'Ramasse agrès',
      [DeliveryStep.DOCUMENTATION]: 'Commentaire client',
      [DeliveryStep.SIGNATURE]: 'Signature',
      [DeliveryStep.SUCCESS]: 'Quitter',
    };

    return backButtonTexts[currentStep] || 'Retour';
  };

  const getPageTitle = () => {
    if (isIncidentMode) {
      return 'Incident de livraison';
    }

    const pageTitles = {
      [DeliveryStep.DELIVERY]: 'Livraison',
      [DeliveryStep.PAYMENT]: 'Paiement',
      [DeliveryStep.EQUIPMENT_DEPOSIT]: 'Dépôt agrès',
      [DeliveryStep.EQUIPMENT_PICKUP]: 'Ramasse agrès',
      [DeliveryStep.DOCUMENTATION]: 'Commentaire client',
      [DeliveryStep.SIGNATURE]: 'Signature',
      [DeliveryStep.SUCCESS]: 'Livraison terminée',
    };

    return pageTitles[currentStep] || 'Livraison';
  };

  const isPaid = () => formData?.paymentData?.isPaid || false;

  const handleIncidentClick = () => setIsIncidentMode(true);

  // Gestionnaires d'étapes uniformisés
  const handleDeliveryNext = () => setCurrentStep(DeliveryStep.PAYMENT);

  const handleDeliveryClientAbsent = () => {
    setIsClientAbsent(true);
    setCurrentStep(DeliveryStep.EQUIPMENT_DEPOSIT);
  };

  const handlePaymentNext = async (paymentData: PaymentData) => {
    setFormData((prev) => (prev ? { ...prev, paymentData } : null));
    await saveAndNavigate({ paymentData }, DeliveryStep.EQUIPMENT_DEPOSIT);
  };

  const handleEquipmentDepositNext = async (equipmentDepositData: UnloadedEquipmentData) => {
    setFormData((prev) => (prev ? { ...prev, equipmentDepositData } : null));
    await saveAndNavigate({ equipmentDepositData }, DeliveryStep.EQUIPMENT_PICKUP);
  };

  const handleEquipmentDepositPrevious = () => {
    if (isClientAbsent) {
      setIsClientAbsent(false);
      setCurrentStep(DeliveryStep.DELIVERY);
    } else {
      setCurrentStep(DeliveryStep.PAYMENT);
    }
  };

  const handleEquipmentPickupNext = async (equipmentPickupData: ReturnedEquipmentData) => {
    setFormData((prev) => (prev ? { ...prev, equipmentPickupData } : null));
    await saveAndNavigate({ equipmentPickupData }, DeliveryStep.DOCUMENTATION);
  };

  const handleEquipmentPickupPrevious = () => setCurrentStep(DeliveryStep.EQUIPMENT_DEPOSIT);

  const captureGPSCoordinates = (): Promise<{
    latitude: number;
    longitude: number;
    timestamp: number;
    accuracy?: number;
  }> => {
    return new Promise((resolve, reject) => {
      if (!navigator.geolocation) {
        reject(new Error('Géolocalisation non supportée'));

        return;
      }

      navigator.geolocation.getCurrentPosition(
        (position) => {
          resolve({
            latitude: position.coords.latitude,
            longitude: position.coords.longitude,
            timestamp: Date.now(),
            accuracy: position.coords.accuracy,
          });
        },
        (error) => {
          console.warn('Erreur GPS:', error);
          resolve({
            latitude: 0,
            longitude: 0,
            timestamp: Date.now(),
            accuracy: undefined,
          });
        },
        {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 60000,
        },
      );
    });
  };

  const handleDocumentationNext = async (photos: string[], comment: string) => {
    const documentationData = { photos, comment };

    setFormData((prev) => (prev ? { ...prev, documentationData } : null));

    try {
      const gpsData = await captureGPSCoordinates();
      setFormData((prev) => (prev ? { ...prev, gpsData } : null));
    } catch (error) {
      console.error('Erreur lors de la capture GPS:', error);
    }

    // Si client absent, skip la signature et aller directement au succès
    if (isClientAbsent) {
      await saveAndNavigate({ documentationData }, DeliveryStep.SUCCESS);
    } else {
      await saveAndNavigate({ documentationData }, DeliveryStep.SIGNATURE);
    }
  };

  const handleSignatureNext = async (signatureData: SignatureData) => {
    setFormData((prev) => (prev ? { ...prev, signatureData } : null));
    await saveAndNavigate({ signatureData }, DeliveryStep.SUCCESS);
  };

  const handleSuccessViewDeliveryNote = () => {
    console.log('Voir bon de livraison pour stop:', formData?.stop.id);
  };

  const handleSuccessFinish = () => history.goBack();

  const handleIncidentNext = (_incidentData: IncidentData) => {
    setFormData(null);
    setCurrentStep(DeliveryStep.DELIVERY);
    setIsIncidentMode(false);
    history.push('/admin/tours');
  };

  const handleIncidentContinueProcess = async (incidentData: IncidentData) => {
    // Sauvegarder les données d'incident et continuer le processus normal
    setFormData((prev) => (prev ? { ...prev, incidentData } : null));
    await saveAndNavigate({ incidentData }, DeliveryStep.PAYMENT);
    setIsIncidentMode(false);
  };

  if (loading || !formData) {
    return (
      <IonPage style={{ paddingTop: '100px', paddingBottom: '100px' }}>
        <IonContent className="ion-padding">
          <div className="flex flex-col items-center justify-center h-full gap-4">
            <div>Chargement de la livraison...</div>

            {isLoadingPersistence && !showRecoveryModal && (
              <div className="mt-8 text-center">
                <div className="text-sm text-gray-600 mb-4">
                  Problème de chargement des données ?
                </div>
                <button
                  onClick={() => setIsLoadingPersistence(false)}
                  className="px-4 py-2 bg-blue-500 text-white rounded text-sm"
                >
                  Ignorer et continuer
                </button>
              </div>
            )}
          </div>
        </IonContent>
      </IonPage>
    );
  }

  function handleButtonLogic(): { onClick: () => void; disabled: boolean } {
    if (isIncidentMode) {
      return {
        onClick: () => handleIncidentNext({} as IncidentData),
        disabled: false,
      };
    }

    switch (currentStep) {
      case DeliveryStep.DELIVERY:
        return {
          onClick: handleDeliveryNext,
          disabled: false,
        };
      case DeliveryStep.PAYMENT:
        return {
          onClick: () => stepPaymentRef.current?.submit(),
          disabled: !stepPaymentRef.current?.isValid(),
        };
      case DeliveryStep.EQUIPMENT_DEPOSIT:
        return {
          onClick: () => stepEquipmentDepositRef.current?.submit(),
          disabled: !stepEquipmentDepositRef.current?.isValid(),
        };
      case DeliveryStep.EQUIPMENT_PICKUP:
        return {
          onClick: () => stepEquipmentPickupRef.current?.submit(),
          disabled: !stepEquipmentPickupRef.current?.isValid(),
        };
      case DeliveryStep.DOCUMENTATION:
        return {
          onClick: () => stepClientCommentRef.current?.submit(),
          disabled: !stepClientCommentRef.current?.isValid(),
        };
      case DeliveryStep.SIGNATURE:
        return {
          onClick: () => stepSignatureRef.current?.submit(),
          disabled: !stepSignatureRef.current?.isValid(),
        };
      case DeliveryStep.SUCCESS:
        return {
          onClick: handleSuccessFinish,
          disabled: false,
        };
      default:
        return {
          onClick: () => {},
          disabled: true,
        };
    }
  }

  return (
    <>
      {formSummary && (
        <FormRecoveryModal
          isOpen={showRecoveryModal}
          onContinue={handleContinueForm}
          onRestart={handleRestartForm}
          onCancel={handleCancelRecovery}
          formSummary={formSummary}
          stopName={formData?.stop?.originalClientInfo?.name}
        />
      )}

      <IonPage className="pt-20 sm:pt-24 md:pt-28 lg:pt-32">
        <IonContent className="ion-padding">
          {currentStep !== DeliveryStep.SUCCESS && (
            <div className="mb-6 pt-2 sm:pt-4 md:pt-6">
              <div className="flex justify-between items-center mb-3 sm:mb-4">
                {canGoBack() ? (
                  <div
                    className="flex items-center gap-2 cursor-pointer p-2 sm:p-3 rounded-lg hover:bg-gray-100 active:bg-gray-200"
                    onClick={handleGlobalBack}
                  >
                    <ChevronLeft size={30} />

                    <IonText color="medium" className="text-xl">
                      {getPageTitle()}
                    </IonText>
                  </div>
                ) : (
                  <div
                    className="flex items-center gap-2 cursor-pointer p-2 sm:p-3 rounded-lg hover:bg-gray-100 active:bg-gray-200"
                    onClick={history.goBack}
                  >
                    <ChevronLeft size={30} />
                    <IonText color="medium" className="text-xl">
                      Retour
                    </IonText>
                  </div>
                )}

                <IonText color="medium" className="text-sm">
                  {formatDate()}
                </IonText>
              </div>

              <div className="flex gap-2 sm:gap-3 md:gap-4 mb-4 sm:mb-6 justify-between flex-wrap">
                <div
                  onClick={handleIncidentClick}
                  className="flex items-center cursor-pointer p-2 sm:p-3 text-red-600 underline rounded-lg hover:bg-gray-100 active:bg-gray-200"
                >
                  <IonIcon
                    icon={warning}
                    size="small"
                    className="text-xl sm:text-2xl md:text-3xl mr-2"
                  />
                  <IonText className="text-sm">Incident</IonText>
                </div>

                <div className="flex items-center gap-2">
                  <IonCheckbox checked={isPaid()} disabled={true} />
                  <IonText className="text-sm sm:text-base font-medium text-gray-800">
                    Comptant
                  </IonText>
                </div>
              </div>
            </div>
          )}

          <div className="pb-6 sm:pb-8 md:pb-10">
            {currentStep === DeliveryStep.DELIVERY && !isIncidentMode && (
              <StepDelivery
                stop={formData.stop}
                onNext={handleDeliveryNext}
                onClientAbsent={handleDeliveryClientAbsent}
              />
            )}

            {currentStep === DeliveryStep.PAYMENT && !isIncidentMode && (
              <StepPayment
                ref={stepPaymentRef}
                stop={formData.stop}
                onNext={handlePaymentNext}
                initialPaymentData={formData.paymentData}
              />
            )}

            {currentStep === DeliveryStep.EQUIPMENT_DEPOSIT && !isIncidentMode && (
              <StepEquipmentDeposit
                ref={stepEquipmentDepositRef}
                stop={formData.stop}
                onNext={handleEquipmentDepositNext}
                onPrevious={handleEquipmentDepositPrevious}
                initialEquipmentData={formData.equipmentDepositData}
              />
            )}

            {currentStep === DeliveryStep.EQUIPMENT_PICKUP && !isIncidentMode && (
              <StepEquipmentPickup
                ref={stepEquipmentPickupRef}
                stop={formData.stop}
                onNext={handleEquipmentPickupNext}
                onPrevious={handleEquipmentPickupPrevious}
                initialEquipmentData={formData.equipmentPickupData}
              />
            )}

            {currentStep === DeliveryStep.DOCUMENTATION && !isIncidentMode && (
              <StepClientComment
                ref={stepClientCommentRef}
                stop={formData.stop}
                onNext={handleDocumentationNext}
                initialPhotos={formData.documentationData?.photos || []}
                initialComment={formData.documentationData?.comment || ''}
              />
            )}

            {currentStep === DeliveryStep.SIGNATURE && !isIncidentMode && (
              <StepSignature
                ref={stepSignatureRef}
                stop={formData.stop}
                onNext={handleSignatureNext}
                initialSignatureData={formData.signatureData}
              />
            )}

            {currentStep === DeliveryStep.SUCCESS && !isIncidentMode && (
              <StepSuccess
                deliveryId={formData.stop.id}
                formData={formData}
                onViewDeliveryNote={handleSuccessViewDeliveryNote}
                onFinish={handleSuccessFinish}
              />
            )}

            {isIncidentMode && (
              <StepIncident
                onNext={handleIncidentNext}
                onCancel={() => setIsIncidentMode(false)}
                onContinueProcess={handleIncidentContinueProcess}
              />
            )}
            {!isIncidentMode && (
              <div className="absolute bottom-12 left-1/2 right-1/2 w-full max-w-3xl transform -translate-x-1/2  px-4 ">
                {(() => {
                  const buttonLogic = handleButtonLogic();

                  return (
                    <InlineButtons
                      buttons={[
                        {
                          label: 'Suivant',
                          onClick: buttonLogic.onClick,
                          classNames: {
                            button: `px-8 sm:px-12 py-5 sm:py-4 ${buttonLogic.disabled ? 'bg-gray-300 text-gray-500 cursor-not-allowed' : 'primary-button'}`,
                          },
                          disabled: buttonLogic.disabled,
                        },
                      ]}
                    />
                  );
                })()}
              </div>
            )}
          </div>
        </IonContent>
      </IonPage>
    </>
  );
};

export default DeliveryProcess;
