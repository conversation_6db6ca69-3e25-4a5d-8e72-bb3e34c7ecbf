import { configureStore } from '@reduxjs/toolkit';
import applicationStateSlice from './applicationStateSlice';
import currentUserSlice from './currentUserSlice';
import delivererLoadingControlSlice from './delivererLoadingControlSlice';
import deliveryNotesSlice from './deliveryNotesSlice';
import eventQueueSlice, { eventQueuePersistMiddleware } from './eventQueueSlice';
import incidentTypesSlice from './incidentTypeSlice';
import logisticEquipmentsSlice from './logisticEquipmentsSlice';
import receptionistPreloadSlice from './receptionistPreloadSlice';
import tourSlice from './tourSlice';

export const store = configureStore({
  reducer: {
    currentUser: currentUserSlice,
    applicationState: applicationStateSlice,
    tour: tourSlice,
    deliveryNotes: deliveryNotesSlice,
    eventQueue: eventQueueSlice,
    logisticEquipments: logisticEquipmentsSlice,
    incidentTypes: incidentTypesSlice,
    receptionistPreload: receptionistPreloadSlice,
    delivererLoadingControl: delivererLoadingControlSlice,
  },
  middleware: (getDefaultMiddleware) => getDefaultMiddleware().concat(eventQueuePersistMiddleware),
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
