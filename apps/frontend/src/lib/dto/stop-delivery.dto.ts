export enum DeliveryCompletionType {
  FULL = 'FULL',
  PARTIAL = 'PARTIAL',
  NONE = 'NONE',
}

export interface IEquipmentCountDto {
  palletCount?: number;
  rollCount?: number;
  packageCount?: number;
}

export interface ILogisticsEquipmentDetailDto {
  logisticsEquipmentTypeId: string;
  quantity: number;
}

export interface ICompleteStopDeliveryDto {
  signatureFile?: {
    base64: string;
    filename: string;
    mimeType: string;
  };
  photoFile?: {
    base64: string;
    filename: string;
    mimeType: string;
  };
  incidentTypeId?: string;
  comments?: string;
  deliveryCompletionType?: DeliveryCompletionType;
  isSecureLocation?: boolean;
  returnedEquipmentCount?: IEquipmentCountDto;
  unloadedEquipmentCount?: IEquipmentCountDto;
  returnedEquipmentDetails?: ILogisticsEquipmentDetailDto[];
  unloadedEquipmentDetails?: ILogisticsEquipmentDetailDto[];
  clientEmail?: string;
}
