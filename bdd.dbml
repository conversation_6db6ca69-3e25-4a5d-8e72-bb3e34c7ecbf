// Enum definitions
enum tour_status {
  planned
  in_progress
  completed
}

enum tour_type {
  normal // e.g. Tour_4012
  frozen // e.g. Tour_4025TK
}

// NOTE: These enums are not implemented yet in actual entities
// enum delivery_note_status {
//   planned // BL is known but not yet processed
//   loaded // Goods for this BL are loaded (if applicable to individual BL processing)
//   completed // BL processing is finished (delivered, undelivered with reason, etc.)
// }

// enum delivery_note_completion {
//   undelivered // BL not delivered
//   partially // BL partially delivered
//   complete // BL fully delivered
// }

enum shipment_operation_type {
  deliver_for // "Livrer pour" from XML <LIGNE_EXPEDITEUR><Operation>
  collect_from // "Charger pour" from XML <LIGNE_EXPEDITEUR><Operation>
}

// Tables
Table user {
  id uuid [pk]
  username text [unique, note: "Keycloak username"]
  // Keycloak properties stored outside DB
  // locale text
  // email text
  // first_name text
  // last_name text
  created_at timestamp [not null, default: `current_timestamp`]
  updated_at timestamp [not null, default: `current_timestamp`]
}

// Table to manage the planning/scheduling of tour assignments to users 
Table tour_assignment {
  id uuid [pk]
  user_id uuid [not null, ref: < user.id, note: "User (driver) assigned"]
  tour_number text [not null, note: "The tour number"]
  tour_type tour_type [not null, note: "The type of the tour (normal, frozen)"]
  tour_original_number text [not null, note: "Original tour number from XML"]
  from_date date [not null, note: "Start date of the assignment period (inclusive)"]
  to_date date [note: "End date of the assignment period (inclusive), nullable"]
  notes text [note: "Optional notes for this specific assignment"]
  
  created_at timestamp [not null, default: `current_timestamp`]
  updated_at timestamp [not null, default: `current_timestamp`]

  // NOTE: Indexes defined in entity but updated here to match entity
  indexes {
    (user_id, tour_number, tour_type, from_date, to_date) [name: "tour_assignment_lookup"]
  }
}

// Les tours sont importé depuis les XML avec le nom de fichier Tour_<tour_number>_DateLivr_<date (AAAAMMJJ)>.xml
Table tour {
  id uuid [pk]
  tour_number text [not null, note: "Processed tour identifier, derived from <TOURNEE>"]
  tour_type tour_type [not null, default: 'normal', note: "Type of tour, derived from <TOURNEE>"]
  tour_original_number text [not null, note: "Original tour number from XML"]
  delivery_date date [not null, note: "Delivery date from XML <DATE_LIVR> tag"]
  status tour_status [not null, default: 'planned']
  provider_file_name text [not null, note: "Original filename from XML"]
  created_at timestamp [not null, default: `current_timestamp`]
  updated_at timestamp [not null, default: `current_timestamp`]

  indexes {
    (tour_number, tour_type, delivery_date) [unique, name: "idx_tour_identifier_lookup"]
  }
}

Table client {
  id uuid [pk]
  code text [unique, not null, note: "From <ClientCode> in XML <ARRET>"]
  name text [not null, note: "From <ClientNom> in XML <ARRET>"]
  email text [note: "From <ClientMAIL> in XML <ARRET>"]
  address_line1 text [note: "From <ClientADR1> in XML <ARRET>"]
  address_line2 text [note: "From <ClientADR2> in XML <ARRET>"]
  address_line3 text [note: "From <ClientADR3> in XML <ARRET>"]
  address_line4 text [note: "From <ClientADR4> in XML <ARRET>"]
  created_at timestamp [not null, default: `current_timestamp`]
  updated_at timestamp [not null, default: `current_timestamp`]
}

Table stop {
  id uuid [pk]
  tour_id uuid [not null, ref: < tour.id, note: "Links stop to its parent tour"]
  sequence_in_tour integer [not null, note: "Order within the tour"]
  client_id uuid [not null, ref: < client.id, note: "FK to the client table"]
  
  // OriginalClientInfo embedded in entity
  original_client_code text [not null, note: "Original ClientCode from XML <ARRET>"]
  original_client_name text [not null, note: "Original ClientNom from XML <ARRET>"]
  original_client_address_line1 text [note: "Original ClientADR1 from XML <ARRET>"]
  original_client_address_line2 text [note: "Original ClientADR2 from XML <ARRET>"]
  original_client_address_line3 text [note: "Original ClientADR3 from XML <ARRET>"]
  original_client_address_line4 text [note: "Original ClientADR4 from XML <ARRET>"]
  original_client_email text [note: "Original ClientMAIL from XML <ARRET>"]

  sorting_code text [note: "From <CodeTriArret> in XML <ARRET>, can be null"]
  delivery_time_window text [note: "From <HeuresLivraison> in XML <ARRET>, e.g., 07:00-10:00, can be null"]
  
  created_at timestamp [not null, default: `current_timestamp`]
  updated_at timestamp [not null, default: `current_timestamp`]

  indexes {
    (tour_id, sequence_in_tour) [unique, name: "unique_stop_in_tour_sequence"]
  }
}

Table shipment_line {
  id uuid [pk]
  stop_id uuid [not null, ref: < stop.id, note: "Links shipment line to its parent stop"]
  operation shipment_operation_type [not null, note: "From <Operation> in XML <LIGNE_EXPEDITEUR>"]
  is_frozen boolean [not null, default: false, note: "From <Surgele> in XML <LIGNE_EXPEDITEUR>"]
  shipper_name text [note: "From <Expediteur> in XML <LIGNE_EXPEDITEUR>"]
  weight_kg decimal [note: "From <Poids> in XML <LIGNE_EXPEDITEUR>, nullable"]
  pallet_count integer [note: "From <NbPalettes> in XML <LIGNE_EXPEDITEUR>, nullable"]
  roll_count integer [note: "From <NbRolls> in XML <LIGNE_EXPEDITEUR>, nullable"]
  package_count integer [note: "From <NbColis> in XML <LIGNE_EXPEDITEUR>, nullable"]
  amount decimal [note: "From <Montant> in XML <LIGNE_EXPEDITEUR>, nullable"]
  
  created_at timestamp [not null, default: `current_timestamp`]
  updated_at timestamp [not null, default: `current_timestamp`]
}

Table file {
  id uuid [pk]
  s3file_key text [unique, not null, note: "Unique storage key"]
  original_filename text [not null, note: "Original filename"]
  file_size bigint [not null, note: "File size in bytes"]
  content_type text [note: "MIME type, nullable"]
  // uploaded_by handled by relationship in entity
  metadata jsonb [note: "Additional metadata, nullable"]
  created_at timestamp [not null, default: `current_timestamp`]
  updated_at timestamp [not null, default: `current_timestamp`]
}

Table delivery_note {
  id uuid [pk]
  stop_id uuid [not null, ref: < stop.id, note: "Links this BL to the specific stop"]
  filename text [not null, note: "Filename from <BL_NOM> in XML"]
  file_id uuid [ref: < file.id, note: "The PDF file for this delivery note", nullable: true]
  
  // Status and completion fields not implemented yet
  // status delivery_note_status [not null, default: 'planned'] 
  // completion delivery_note_completion [not null, default: 'undelivered']
  
  // Further details not implemented yet
  // loaded_at timestamp
  // completed_at timestamp
  
  created_at timestamp [not null, default: `current_timestamp`]
  updated_at timestamp [not null, default: `current_timestamp`]
}

// Attachment table not implemented yet
// Table attachment {
//   id uuid [pk]
//   file_id uuid [not null, ref: < file.id]
//   delivery_note_id uuid [not null, ref: < delivery_note.id]
//   attachment_type text
//   description text
//   created_at timestamp [not null, default: `current_timestamp`]
//   updated_at timestamp [not null, default: `current_timestamp`]
// }

